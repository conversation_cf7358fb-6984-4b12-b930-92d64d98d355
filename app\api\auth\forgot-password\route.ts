import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { validateEmail, generateRandomString } from "@/lib/utils"
import { sendPasswordResetEmail } from "@/lib/email"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = body

    // Validation
    if (!email) {
      return NextResponse.json(
        { error: "Email is required" },
        { status: 400 }
      )
    }

    if (!validateEmail(email)) {
      return NextResponse.json(
        { error: "Invalid email format" },
        { status: 400 }
      )
    }

    // Find user
    const user = await db.user.findUnique({
      where: { email }
    })

    // Always return success to prevent email enumeration
    if (!user) {
      return NextResponse.json({
        success: true,
        message: "If an account with that email exists, we've sent a password reset link."
      })
    }

    // Check if user is active
    if (!user.isActive) {
      return NextResponse.json({
        success: true,
        message: "If an account with that email exists, we've sent a password reset link."
      })
    }

    // Generate reset token
    const resetToken = generateRandomString(64)
    const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000) // 1 hour

    // Store reset token in database
    await db.verificationToken.create({
      data: {
        identifier: email,
        token: resetToken,
        expires: resetTokenExpiry
      }
    })

    // Send password reset email
    try {
      await sendPasswordResetEmail(email, user.name || email, resetToken)
    } catch (emailError) {
      console.error("Failed to send password reset email:", emailError)
      // Don't expose email sending errors to the user
    }

    // Log password reset request
    await db.auditLog.create({
      data: {
        userId: user.id,
        action: "PASSWORD_RESET_REQUEST",
        resource: "USER",
        resourceId: user.id,
        details: {
          email: user.email,
          timestamp: new Date().toISOString()
        },
        ipAddress: request.headers.get("x-forwarded-for") || "unknown"
      }
    })

    return NextResponse.json({
      success: true,
      message: "If an account with that email exists, we've sent a password reset link."
    })

  } catch (error) {
    console.error("Forgot password error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
