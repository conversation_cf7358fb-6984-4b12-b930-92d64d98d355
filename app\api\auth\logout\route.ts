import { NextRequest, NextResponse } from "next/server"
import jwt from "jsonwebtoken"
import { db } from "@/lib/db"

export async function POST(request: NextRequest) {
  try {
    // Get token from cookie
    const token = request.cookies.get("auth-token")?.value

    if (token) {
      try {
        // Decode token to get user info for logging
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
        
        // Log logout
        await db.auditLog.create({
          data: {
            userId: decoded.userId,
            action: "LOGOUT",
            resource: "USER",
            resourceId: decoded.userId,
            details: {
              email: decoded.email,
              timestamp: new Date().toISOString()
            },
            ipAddress: request.headers.get("x-forwarded-for") || "unknown"
          }
        })
      } catch (error) {
        // Token might be invalid, but we still want to clear the cookie
        console.error("Error logging logout:", error)
      }
    }

    // Create response
    const response = NextResponse.json({
      success: true,
      message: "Logged out successfully"
    })

    // Clear the auth cookie
    response.cookies.set("auth-token", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 0 // Expire immediately
    })

    return response

  } catch (error) {
    console.error("Logout error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
