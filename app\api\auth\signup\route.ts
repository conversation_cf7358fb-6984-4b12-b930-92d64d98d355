import { NextRequest, NextResponse } from "next/server"
import bcrypt from "bcryptjs"
import { db } from "@/lib/db"
import { validateEmail, validatePassword } from "@/lib/utils"
// import { UserRole } from "@prisma/client"
type UserRole = "STUDENT" | "INSTRUCTOR" | "PROCTOR" | "ADMIN"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password, name, role, accessCode, ...additionalData } = body

    // Validation
    if (!email || !password || !name || !role) {
      return NextResponse.json(
        { error: "All fields are required" },
        { status: 400 }
      )
    }

    if (!validateEmail(email)) {
      return NextResponse.json(
        { error: "Invalid email format" },
        { status: 400 }
      )
    }

    const passwordValidation = validatePassword(password)
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { error: passwordValidation.errors[0] },
        { status: 400 }
      )
    }

    const validRoles: UserRole[] = ["STUDENT", "INSTRUCTOR", "PROCTOR", "ADMIN"]
    if (!validRoles.includes(role as UserRole)) {
      return NextResponse.json(
        { error: "Invalid role" },
        { status: 400 }
      )
    }

    // Check access codes for privileged roles
    if (role !== UserRole.STUDENT) {
      if (!accessCode) {
        return NextResponse.json(
          { error: "Access code is required for this role" },
          { status: 400 }
        )
      }

      // Validate access codes (in production, these should be stored securely)
      const validAccessCodes = {
        [UserRole.ADMIN]: process.env.ADMIN_ACCESS_CODE || "ADMIN2024",
        [UserRole.INSTRUCTOR]: process.env.INSTRUCTOR_ACCESS_CODE || "INSTRUCTOR2024",
        [UserRole.PROCTOR]: process.env.PROCTOR_ACCESS_CODE || "PROCTOR2024"
      }

      if (accessCode !== validAccessCodes[role]) {
        return NextResponse.json(
          { error: "Invalid access code" },
          { status: 403 }
        )
      }
    }

    // Role-specific validation
    if (role === 'INSTRUCTOR' && !additionalData.employeeId) {
      return NextResponse.json(
        { error: "Employee ID is required for instructors" },
        { status: 400 }
      )
    }

    if (role === 'PROCTOR' && !additionalData.phone) {
      return NextResponse.json(
        { error: "Phone number is required for proctors" },
        { status: 400 }
      )
    }

    if (role === 'ADMIN' && (!additionalData.position || !additionalData.employeeId)) {
      return NextResponse.json(
        { error: "Position and Employee ID are required for administrators" },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 409 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Determine if account needs approval
    const needsApproval = role !== 'STUDENT'

    // Create user with profile
    const user = await db.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
        role,
        isActive: role === 'STUDENT', // Students are active immediately, others need approval
        profile: {
          create: {
            firstName: name.split(' ')[0] || name,
            lastName: name.split(' ').slice(1).join(' ') || '',
            phone: additionalData.phone || null,
          }
        }
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true
      }
    })

    // Different response messages based on role
    let message = "Account created successfully"
    if (needsApproval) {
      message = "Account created successfully. Your account is pending approval and will be activated within 2-3 business days."
    }

    return NextResponse.json({
      success: true,
      message,
      user,
      needsApproval
    })

  } catch (error) {
    console.error("Signup error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
