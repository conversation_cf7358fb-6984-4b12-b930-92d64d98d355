import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { withAuth } from "@/lib/middleware"

// GET - Get user profile
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const userProfile = await db.user.findUnique({
      where: { id: user.userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
        profile: true
      }
    })

    if (!userProfile) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      user: userProfile
    })

  } catch (error) {
    console.error("Get profile error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
})

// PUT - Update user profile
export const PUT = withAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const { 
      name, 
      firstName, 
      lastName, 
      phone, 
      dateOfBirth, 
      address 
    } = body

    // Update user basic info
    const updatedUser = await db.user.update({
      where: { id: user.userId },
      data: {
        name,
        profile: {
          upsert: {
            create: {
              firstName,
              lastName,
              phone,
              dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
              address
            },
            update: {
              firstName,
              lastName,
              phone,
              dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
              address
            }
          }
        }
      },
      include: {
        profile: true
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
        profile: true
      }
    })

    // Log profile update
    await db.auditLog.create({
      data: {
        userId: user.userId,
        action: "UPDATE",
        resource: "USER_PROFILE",
        resourceId: user.userId,
        details: {
          updatedFields: Object.keys(body),
          timestamp: new Date().toISOString()
        },
        ipAddress: request.headers.get("x-forwarded-for") || "unknown"
      }
    })

    return NextResponse.json({
      success: true,
      message: "Profile updated successfully",
      user: updatedUser
    })

  } catch (error) {
    console.error("Update profile error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
})
