"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import Link from "next/link"

export default function DemoPage() {
  const [selectedDemo, setSelectedDemo] = useState("student")
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    role: "",
    interests: []
  })
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)

  const demoTypes = [
    {
      id: "student",
      title: "Student Experience",
      description: "See how students take exams with our intuitive interface",
      icon: "🎓",
      features: [
        "Exam taking interface",
        "Question navigation",
        "Time management",
        "Progress tracking",
        "Submission process"
      ]
    },
    {
      id: "instructor",
      title: "Instructor Dashboard",
      description: "Explore exam creation and management tools",
      icon: "👨‍🏫",
      features: [
        "Exam creation wizard",
        "Question bank management",
        "Student analytics",
        "Grading interface",
        "Report generation"
      ]
    },
    {
      id: "proctor",
      title: "Proctoring System",
      description: "Experience our advanced monitoring capabilities",
      icon: "👁️",
      features: [
        "Live monitoring dashboard",
        "AI behavior detection",
        "Flag management",
        "Recording playback",
        "Incident reporting"
      ]
    },
    {
      id: "admin",
      title: "Admin Panel",
      description: "Manage users, settings, and system configuration",
      icon: "⚙️",
      features: [
        "User management",
        "System settings",
        "Analytics dashboard",
        "Audit logs",
        "Security controls"
      ]
    }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setSuccess(true)
    } catch (error) {
      console.error("Demo request failed:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md w-full mx-4">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">✅</span>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Demo Scheduled!</h2>
              <p className="text-gray-600 mb-4">
                Thank you for your interest! We'll contact you within 24 hours to schedule your personalized demo.
              </p>
              <p className="text-sm text-gray-500 mb-6">
                Check your email for confirmation details and preparation instructions.
              </p>
              <Link href="/">
                <Button className="w-full">
                  Return to Home
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            See Our Platform
            <span className="text-primary block">In Action</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Experience the power of our online examination platform with interactive demos and personalized walkthroughs.
          </p>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* Demo Types */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Choose Your Demo Experience
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Explore different aspects of our platform based on your role and interests
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {demoTypes.map((demo) => (
              <Card 
                key={demo.id} 
                className={`cursor-pointer transition-all ${
                  selectedDemo === demo.id 
                    ? 'ring-2 ring-primary shadow-lg' 
                    : 'hover:shadow-md'
                }`}
                onClick={() => setSelectedDemo(demo.id)}
              >
                <CardHeader className="text-center">
                  <div className="text-4xl mb-2">{demo.icon}</div>
                  <CardTitle className="text-lg">{demo.title}</CardTitle>
                  <CardDescription>{demo.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {demo.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm">
                        <span className="text-green-500 mr-2">✓</span>
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Interactive Demo Preview */}
          <Card className="mb-12">
            <CardHeader>
              <CardTitle className="text-2xl">
                {demoTypes.find(d => d.id === selectedDemo)?.title} Preview
              </CardTitle>
              <CardDescription>
                Interactive walkthrough of key features and capabilities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-100 rounded-lg p-8 text-center">
                <div className="text-6xl mb-4">
                  {demoTypes.find(d => d.id === selectedDemo)?.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Interactive Demo Coming Soon
                </h3>
                <p className="text-gray-600 mb-6">
                  We're building an interactive demo experience. For now, schedule a live demo with our team to see the platform in action.
                </p>
                <Button size="lg">
                  Schedule Live Demo
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Demo Request Form */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">Schedule Your Demo</CardTitle>
                <CardDescription>
                  Get a personalized walkthrough tailored to your needs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name *
                      </label>
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        required
                        value={formData.name}
                        onChange={handleChange}
                        disabled={loading}
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address *
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        required
                        value={formData.email}
                        onChange={handleChange}
                        disabled={loading}
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
                        Institution/Company *
                      </label>
                      <Input
                        id="company"
                        name="company"
                        type="text"
                        required
                        value={formData.company}
                        onChange={handleChange}
                        disabled={loading}
                        placeholder="Your institution"
                      />
                    </div>
                    <div>
                      <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                        Your Role *
                      </label>
                      <select
                        id="role"
                        name="role"
                        required
                        value={formData.role}
                        onChange={handleChange}
                        disabled={loading}
                        className="w-full px-3 py-2 border border-input bg-background rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                      >
                        <option value="">Select your role</option>
                        <option value="administrator">Administrator</option>
                        <option value="instructor">Instructor</option>
                        <option value="it-manager">IT Manager</option>
                        <option value="decision-maker">Decision Maker</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      What interests you most? (Select all that apply)
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {[
                        "Advanced Proctoring",
                        "Question Creation",
                        "Analytics & Reporting",
                        "Security Features",
                        "Integration Options",
                        "Pricing & Plans"
                      ].map((interest) => (
                        <label key={interest} className="flex items-center">
                          <input
                            type="checkbox"
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            disabled={loading}
                          />
                          <span className="ml-2 text-sm text-gray-700">{interest}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-900 mb-2">What to Expect:</h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• 30-45 minute personalized demo</li>
                      <li>• Live Q&A with our product experts</li>
                      <li>• Custom use case scenarios</li>
                      <li>• Pricing discussion tailored to your needs</li>
                      <li>• Next steps and trial setup</li>
                    </ul>
                  </div>

                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? "Scheduling..." : "Schedule My Demo"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Demo Information */}
          <div className="space-y-8">
            {/* Why Demo */}
            <Card>
              <CardHeader>
                <CardTitle>Why Schedule a Demo?</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <span className="text-2xl">🎯</span>
                    <div>
                      <h4 className="font-semibold text-gray-900">Personalized Experience</h4>
                      <p className="text-gray-600 text-sm">See features relevant to your specific use case and requirements</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <span className="text-2xl">💡</span>
                    <div>
                      <h4 className="font-semibold text-gray-900">Expert Guidance</h4>
                      <p className="text-gray-600 text-sm">Get insights from our product experts and best practices</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <span className="text-2xl">🚀</span>
                    <div>
                      <h4 className="font-semibold text-gray-900">Fast Implementation</h4>
                      <p className="text-gray-600 text-sm">Learn how to get started quickly with your specific setup</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Testimonials */}
            <Card>
              <CardHeader>
                <CardTitle>What Others Say</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <blockquote className="border-l-4 border-primary pl-4">
                    <p className="text-gray-600 italic text-sm">
                      "The demo was incredibly helpful. We could see exactly how the platform would work for our specific needs."
                    </p>
                    <footer className="text-sm text-gray-500 mt-2">
                      — Dr. Sarah Johnson, University of Technology
                    </footer>
                  </blockquote>
                  <blockquote className="border-l-4 border-primary pl-4">
                    <p className="text-gray-600 italic text-sm">
                      "The team answered all our questions and showed us features we didn't even know we needed."
                    </p>
                    <footer className="text-sm text-gray-500 mt-2">
                      — Michael Chen, Education Director
                    </footer>
                  </blockquote>
                </div>
              </CardContent>
            </Card>

            {/* Alternative Options */}
            <Card>
              <CardHeader>
                <CardTitle>Other Ways to Explore</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Link href="/signup">
                    <Button variant="outline" className="w-full justify-start">
                      <span className="mr-2">🆓</span>
                      Start Free Trial (No Demo Required)
                    </Button>
                  </Link>
                  <Link href="/features">
                    <Button variant="outline" className="w-full justify-start">
                      <span className="mr-2">📋</span>
                      Browse Feature List
                    </Button>
                  </Link>
                  <Link href="/help">
                    <Button variant="outline" className="w-full justify-start">
                      <span className="mr-2">📚</span>
                      Read Documentation
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
