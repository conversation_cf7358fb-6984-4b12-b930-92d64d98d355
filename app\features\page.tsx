import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default function FeaturesPage() {
  const coreFeatures = [
    {
      icon: "🔒",
      title: "Advanced Proctoring",
      description: "AI-powered monitoring with facial recognition, eye tracking, and behavior analysis",
      features: [
        "Real-time face detection and verification",
        "Suspicious behavior flagging",
        "Screen sharing prevention",
        "Multiple monitor detection",
        "Audio monitoring and analysis"
      ]
    },
    {
      icon: "📝",
      title: "Flexible Question Types",
      description: "Support for diverse assessment formats and multimedia content",
      features: [
        "Multiple choice questions",
        "Essay and long-form responses",
        "Code submission and testing",
        "Image and video uploads",
        "Mathematical equation support"
      ]
    },
    {
      icon: "📊",
      title: "Advanced Analytics",
      description: "Comprehensive reporting and insights for educators and administrators",
      features: [
        "Real-time performance dashboards",
        "Detailed score breakdowns",
        "Cheating attempt detection",
        "Time analysis and patterns",
        "Comparative performance metrics"
      ]
    },
    {
      icon: "⚡",
      title: "Automated Grading",
      description: "Instant scoring with intelligent feedback generation",
      features: [
        "Immediate objective question grading",
        "Plagiarism detection for essays",
        "Code compilation and testing",
        "Rubric-based assessment",
        "Bulk grading operations"
      ]
    },
    {
      icon: "🌐",
      title: "Multi-Platform Support",
      description: "Seamless experience across all devices and operating systems",
      features: [
        "Web-based platform (no downloads)",
        "Mobile-responsive design",
        "Cross-browser compatibility",
        "Offline capability for emergencies",
        "API integration support"
      ]
    },
    {
      icon: "🔐",
      title: "Enterprise Security",
      description: "Bank-level security with comprehensive data protection",
      features: [
        "End-to-end encryption",
        "SOC 2 Type II compliance",
        "GDPR and FERPA compliant",
        "Regular security audits",
        "Secure data centers"
      ]
    }
  ]

  const additionalFeatures = [
    {
      category: "Exam Management",
      items: [
        "Question bank with tagging and search",
        "Randomized question pools",
        "Time limits and scheduling",
        "Multiple attempt configurations",
        "Bulk student enrollment"
      ]
    },
    {
      category: "Student Experience",
      items: [
        "Practice mode and tutorials",
        "Progress saving and resume",
        "Accessibility features (screen readers, etc.)",
        "Multi-language support",
        "Technical support chat"
      ]
    },
    {
      category: "Institution Management",
      items: [
        "Role-based access control",
        "Custom branding options",
        "Integration with LMS systems",
        "Bulk user management",
        "Audit trails and compliance reporting"
      ]
    },
    {
      category: "Technical Features",
      items: [
        "99.9% uptime guarantee",
        "Auto-scaling infrastructure",
        "Real-time collaboration tools",
        "Backup and disaster recovery",
        "24/7 monitoring and support"
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Powerful Features for
            <span className="text-primary block">Modern Education</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Everything you need to deliver secure, engaging, and effective online examinations. 
            From advanced proctoring to intelligent analytics, we've got you covered.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signup">
              <Button size="lg" className="text-lg px-8 py-3">
                Try All Features Free
              </Button>
            </Link>
            <Link href="/demo">
              <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                Watch Demo
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Core Platform Features
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Essential tools that make online examinations secure, reliable, and effective
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {coreFeatures.map((feature, index) => (
              <Card key={index} className="h-full hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.features.map((item, idx) => (
                      <li key={idx} className="flex items-start">
                        <span className="text-green-500 mr-2 mt-1">✓</span>
                        <span className="text-sm text-gray-600">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Features */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Complete Feature Set
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Additional capabilities that enhance every aspect of online assessment
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {additionalFeatures.map((category, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-xl">{category.category}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {category.items.map((item, idx) => (
                      <li key={idx} className="flex items-start">
                        <span className="text-primary mr-3 mt-1">•</span>
                        <span className="text-gray-600">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Feature Comparison */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Our Platform?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See how we compare to traditional testing methods and competitors
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full bg-white rounded-lg shadow-lg">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Feature</th>
                  <th className="px-6 py-4 text-center text-sm font-semibold text-primary">Our Platform</th>
                  <th className="px-6 py-4 text-center text-sm font-semibold text-gray-600">Traditional Testing</th>
                  <th className="px-6 py-4 text-center text-sm font-semibold text-gray-600">Basic Online Tools</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900">Advanced Proctoring</td>
                  <td className="px-6 py-4 text-center text-green-600">✓</td>
                  <td className="px-6 py-4 text-center text-red-500">✗</td>
                  <td className="px-6 py-4 text-center text-red-500">✗</td>
                </tr>
                <tr className="bg-gray-50">
                  <td className="px-6 py-4 text-sm text-gray-900">Real-time Analytics</td>
                  <td className="px-6 py-4 text-center text-green-600">✓</td>
                  <td className="px-6 py-4 text-center text-red-500">✗</td>
                  <td className="px-6 py-4 text-center text-yellow-500">Limited</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900">Automated Grading</td>
                  <td className="px-6 py-4 text-center text-green-600">✓</td>
                  <td className="px-6 py-4 text-center text-red-500">✗</td>
                  <td className="px-6 py-4 text-center text-yellow-500">Basic</td>
                </tr>
                <tr className="bg-gray-50">
                  <td className="px-6 py-4 text-sm text-gray-900">Scalability</td>
                  <td className="px-6 py-4 text-center text-green-600">Unlimited</td>
                  <td className="px-6 py-4 text-center text-red-500">Limited</td>
                  <td className="px-6 py-4 text-center text-yellow-500">Moderate</td>
                </tr>
                <tr>
                  <td className="px-6 py-4 text-sm text-gray-900">Security & Compliance</td>
                  <td className="px-6 py-4 text-center text-green-600">Enterprise</td>
                  <td className="px-6 py-4 text-center text-yellow-500">Physical</td>
                  <td className="px-6 py-4 text-center text-yellow-500">Basic</td>
                </tr>
                <tr className="bg-gray-50">
                  <td className="px-6 py-4 text-sm text-gray-900">Cost Efficiency</td>
                  <td className="px-6 py-4 text-center text-green-600">High</td>
                  <td className="px-6 py-4 text-center text-red-500">Low</td>
                  <td className="px-6 py-4 text-center text-yellow-500">Medium</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Experience All Features Today
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Start your free trial and discover how our comprehensive feature set can transform your online assessments.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signup">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
                Start Free Trial
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="text-lg px-8 py-3 border-white text-white hover:bg-white hover:text-primary">
                Schedule Demo
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
