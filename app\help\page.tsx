"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import Link from "next/link"

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")

  const categories = [
    { id: "all", name: "All Topics", icon: "📚" },
    { id: "getting-started", name: "Getting Started", icon: "🚀" },
    { id: "exams", name: "Exams & Testing", icon: "📝" },
    { id: "proctoring", name: "Proctoring", icon: "👁️" },
    { id: "technical", name: "Technical Issues", icon: "🔧" },
    { id: "account", name: "Account & Billing", icon: "👤" },
    { id: "security", name: "Security & Privacy", icon: "🔒" }
  ]

  const faqs = [
    {
      category: "getting-started",
      question: "How do I create my first exam?",
      answer: "To create your first exam: 1) Log into your instructor dashboard, 2) Click 'Create New Exam', 3) Fill in exam details like title, duration, and instructions, 4) Add questions from your question bank or create new ones, 5) Configure proctoring settings if needed, 6) Set the exam schedule and publish when ready."
    },
    {
      category: "getting-started",
      question: "What system requirements do I need?",
      answer: "Our platform works on any modern web browser (Chrome, Firefox, Safari, Edge). For proctored exams, you'll need: a webcam, microphone, stable internet connection (minimum 1 Mbps), and the latest browser version. Mobile devices are supported for basic functionality."
    },
    {
      category: "exams",
      question: "Can students save their progress during an exam?",
      answer: "Yes! Our platform automatically saves student responses every 30 seconds. Students can also manually save their progress. If there's a technical issue, they can resume from where they left off within the exam time limit."
    },
    {
      category: "exams",
      question: "How do I set up different question types?",
      answer: "We support multiple question types: Multiple Choice (single/multiple answers), True/False, Short Answer, Essay, Fill-in-the-blank, and File Upload. When creating questions, select the type from the dropdown and follow the specific formatting guidelines for each type."
    },
    {
      category: "proctoring",
      question: "How does AI proctoring work?",
      answer: "Our AI proctoring monitors students through their webcam and microphone, detecting suspicious behaviors like looking away from screen, multiple faces, unusual sounds, or attempts to switch applications. Flags are generated for review, but human proctors make final decisions on violations."
    },
    {
      category: "proctoring",
      question: "What happens if a student's camera stops working during an exam?",
      answer: "If technical issues occur during a proctored exam, the student should immediately contact support via the help chat. The exam will be paused, and our technical team will help resolve the issue. The student can resume with the remaining time once the problem is fixed."
    },
    {
      category: "technical",
      question: "Why can't I access my exam?",
      answer: "Common reasons include: 1) Exam hasn't started yet or has ended, 2) Browser compatibility issues (try Chrome), 3) Network connectivity problems, 4) Account permissions, 5) System maintenance. Check your internet connection and try refreshing the page first."
    },
    {
      category: "technical",
      question: "The platform is running slowly. What should I do?",
      answer: "Try these steps: 1) Close other browser tabs and applications, 2) Check your internet speed (minimum 1 Mbps required), 3) Clear browser cache and cookies, 4) Disable browser extensions, 5) Try a different browser. If issues persist, contact technical support."
    },
    {
      category: "account",
      question: "How do I reset my password?",
      answer: "Click 'Forgot Password' on the login page, enter your email address, and we'll send you a reset link. The link expires in 1 hour for security. If you don't receive the email, check your spam folder or contact support."
    },
    {
      category: "account",
      question: "Can I change my subscription plan?",
      answer: "Yes! You can upgrade or downgrade your plan anytime from your account settings. Changes take effect immediately for upgrades, or at the next billing cycle for downgrades. Contact our sales team for custom enterprise solutions."
    },
    {
      category: "security",
      question: "How secure is my data?",
      answer: "We use enterprise-grade security including end-to-end encryption, SOC 2 Type II compliance, regular security audits, and secure data centers. All data is encrypted in transit and at rest. We're also GDPR and FERPA compliant for educational data protection."
    },
    {
      category: "security",
      question: "Who can see my exam recordings?",
      answer: "Proctoring recordings are only accessible to authorized personnel: the exam instructor, assigned proctors, and system administrators for technical support. Recordings are automatically deleted after the retention period set by your institution (typically 2 years)."
    }
  ]

  const quickLinks = [
    {
      title: "Platform Status",
      description: "Check current system status and uptime",
      icon: "🟢",
      link: "/status"
    },
    {
      title: "Video Tutorials",
      description: "Step-by-step guides for common tasks",
      icon: "🎥",
      link: "/tutorials"
    },
    {
      title: "API Documentation",
      description: "Technical documentation for developers",
      icon: "📖",
      link: "/docs/api"
    },
    {
      title: "Contact Support",
      description: "Get help from our support team",
      icon: "💬",
      link: "/contact"
    }
  ]

  const filteredFaqs = faqs.filter(faq => {
    const matchesCategory = selectedCategory === "all" || faq.category === selectedCategory
    const matchesSearch = searchQuery === "" || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Help Center
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Find answers to common questions, tutorials, and get the support you need to succeed with our platform.
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search for help articles, tutorials, or FAQs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-4 py-4 text-lg"
              />
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                <span className="text-gray-400 text-xl">🔍</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* Quick Links */}
        <section className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Quick Links</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickLinks.map((link, index) => (
              <Link key={index} href={link.link}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl mb-3">{link.icon}</div>
                    <h3 className="font-semibold text-gray-900 mb-2">{link.title}</h3>
                    <p className="text-gray-600 text-sm">{link.description}</p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </section>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Categories Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                        selectedCategory === category.id
                          ? "bg-primary text-white"
                          : "hover:bg-gray-100"
                      }`}
                    >
                      <span className="mr-2">{category.icon}</span>
                      {category.name}
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* FAQ Content */}
          <div className="lg:col-span-3">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Frequently Asked Questions
              </h2>
              <p className="text-gray-600">
                {filteredFaqs.length} article{filteredFaqs.length !== 1 ? 's' : ''} found
                {selectedCategory !== "all" && ` in ${categories.find(c => c.id === selectedCategory)?.name}`}
                {searchQuery && ` matching "${searchQuery}"`}
              </p>
            </div>

            {filteredFaqs.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <div className="text-6xl mb-4">🔍</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">No articles found</h3>
                  <p className="text-gray-600 mb-4">
                    Try adjusting your search terms or browse different categories.
                  </p>
                  <Button onClick={() => { setSearchQuery(""); setSelectedCategory("all"); }}>
                    Clear Filters
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredFaqs.map((faq, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle className="text-lg">{faq.question}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Still Need Help */}
        <section className="mt-20">
          <Card className="bg-primary text-white">
            <CardContent className="p-12 text-center">
              <h2 className="text-3xl font-bold mb-4">Still Need Help?</h2>
              <p className="text-xl mb-8 opacity-90">
                Can't find what you're looking for? Our support team is here to help.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact">
                  <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
                    Contact Support
                  </Button>
                </Link>
                <Button size="lg" variant="outline" className="text-lg px-8 py-3 border-white text-white hover:bg-white hover:text-primary">
                  Live Chat
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}
