"use client"

import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function InstructorDashboard() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push("/login")
    } else if (!loading && user && user.role !== "INSTRUCTOR") {
      router.push(`/${user.role.toLowerCase()}/dashboard`)
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Instructor Dashboard
          </h1>
          <p className="text-gray-600 mt-2">
            Welcome back, {user.name || user.email}! Manage your courses and exams.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <span className="text-2xl">📚</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Courses</p>
                  <p className="text-2xl font-bold text-gray-900">5</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <span className="text-2xl">📝</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Exams</p>
                  <p className="text-2xl font-bold text-gray-900">23</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <span className="text-2xl">👥</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Students</p>
                  <p className="text-2xl font-bold text-gray-900">156</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <span className="text-2xl">⏰</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending Reviews</p>
                  <p className="text-2xl font-bold text-gray-900">8</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Exams */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Exams</CardTitle>
              <CardDescription>
                Your latest examinations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold text-gray-900">Mathematics Final</h3>
                    <p className="text-sm text-gray-600">Advanced Calculus</p>
                    <p className="text-sm text-gray-500">45 submissions</p>
                  </div>
                  <div className="flex flex-col gap-2">
                    <Link href="/instructor/exam/1/analytics">
                      <Button size="sm">View Analytics</Button>
                    </Link>
                  </div>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold text-gray-900">Physics Midterm</h3>
                    <p className="text-sm text-gray-600">Quantum Physics</p>
                    <p className="text-sm text-gray-500">32 submissions</p>
                  </div>
                  <div className="flex flex-col gap-2">
                    <Link href="/instructor/exam/2/analytics">
                      <Button size="sm">View Analytics</Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common instructor tasks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4">
                <Link href="/instructor/exam/new">
                  <Button className="w-full h-16 flex items-center justify-start px-6">
                    <span className="text-2xl mr-4">➕</span>
                    <div className="text-left">
                      <div className="font-semibold">Create New Exam</div>
                      <div className="text-sm opacity-80">Set up a new examination</div>
                    </div>
                  </Button>
                </Link>
                <Link href="/instructor/questions">
                  <Button variant="outline" className="w-full h-16 flex items-center justify-start px-6">
                    <span className="text-2xl mr-4">❓</span>
                    <div className="text-left">
                      <div className="font-semibold">Question Bank</div>
                      <div className="text-sm opacity-80">Manage your questions</div>
                    </div>
                  </Button>
                </Link>
                <Link href="/instructor/courses">
                  <Button variant="outline" className="w-full h-16 flex items-center justify-start px-6">
                    <span className="text-2xl mr-4">📖</span>
                    <div className="text-left">
                      <div className="font-semibold">Manage Courses</div>
                      <div className="text-sm opacity-80">View and edit courses</div>
                    </div>
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Pending Reviews */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Pending Reviews</CardTitle>
            <CardDescription>
              Exams requiring your attention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg bg-yellow-50">
                <div>
                  <h3 className="font-semibold text-gray-900">Essay Questions - History Exam</h3>
                  <p className="text-sm text-gray-600">8 submissions need manual grading</p>
                  <p className="text-sm text-gray-500">Due: Tomorrow</p>
                </div>
                <Link href="/instructor/exam/3/grade">
                  <Button>Grade Now</Button>
                </Link>
              </div>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-semibold text-gray-900">Flagged Submissions</h3>
                  <p className="text-sm text-gray-600">3 submissions flagged by proctor</p>
                  <p className="text-sm text-gray-500">Requires review</p>
                </div>
                <Link href="/instructor/exam/4/flags">
                  <Button variant="outline">Review</Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
