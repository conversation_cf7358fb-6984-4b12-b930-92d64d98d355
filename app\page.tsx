import Link from "next/link";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function Home() {
  const features = [
    {
      title: "Secure Proctoring",
      description: "Advanced AI-powered monitoring with real-time face detection and behavior analysis.",
      icon: "🔒"
    },
    {
      title: "Flexible Question Types",
      description: "Support for multiple choice, essays, coding questions, and multimedia content.",
      icon: "❓"
    },
    {
      title: "Real-time Analytics",
      description: "Comprehensive reporting and analytics for instructors and administrators.",
      icon: "📊"
    },
    {
      title: "Multi-device Support",
      description: "Works seamlessly across desktop, tablet, and mobile devices.",
      icon: "📱"
    },
    {
      title: "Automated Grading",
      description: "Instant grading for objective questions with detailed feedback.",
      icon: "⚡"
    },
    {
      title: "Scalable Infrastructure",
      description: "Handle thousands of concurrent examinations with 99.9% uptime.",
      icon: "🚀"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Secure Online
              <span className="text-primary block">Examinations</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Comprehensive examination platform with advanced proctoring, real-time monitoring,
              and secure assessment delivery for educational institutions and organizations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/signup">
                <Button size="lg" className="text-lg px-8 py-3">
                  Student Registration
                </Button>
              </Link>
              <Link href="/demo">
                <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                  Watch Demo
                </Button>
              </Link>
            </div>
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-600">
                Are you faculty or staff?{" "}
                <Link href="/instructor/signup" className="text-primary hover:text-primary/80 font-medium">
                  Instructor Registration
                </Link>
                {" | "}
                <Link href="/proctor/signup" className="text-primary hover:text-primary/80 font-medium">
                  Proctor Registration
                </Link>
                {" | "}
                <Link href="/admin/signup" className="text-primary hover:text-primary/80 font-medium">
                  Admin Registration
                </Link>
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need for Online Exams
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              From question creation to result analysis, our platform provides all the tools
              you need for successful online examinations.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Transform Your Examinations?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join thousands of educational institutions already using our platform
            to conduct secure, reliable online examinations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signup">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
                Start Free Trial
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="text-lg px-8 py-3 border-white text-white hover:bg-white hover:text-primary">
                Contact Sales
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
