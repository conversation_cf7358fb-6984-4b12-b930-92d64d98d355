"use client"

import { useAuth } from "@/contexts/auth-context"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function ProctorDashboard() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push("/login")
    } else if (!loading && user && user.role !== "PROCTOR") {
      router.push(`/${user.role.toLowerCase()}/dashboard`)
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const activeSessions = [
    {
      id: "1",
      studentName: "<PERSON>",
      examTitle: "Mathematics Final",
      startTime: "10:00 AM",
      duration: "2 hours",
      status: "active",
      flags: 0
    },
    {
      id: "2", 
      studentName: "Jane <PERSON>",
      examTitle: "Physics Midterm",
      startTime: "2:00 PM",
      duration: "1.5 hours", 
      status: "active",
      flags: 2
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Proctor Dashboard
          </h1>
          <p className="text-gray-600 mt-2">
            Welcome back, {user.name || user.email}! Monitor active exam sessions.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <span className="text-2xl">👁️</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Sessions</p>
                  <p className="text-2xl font-bold text-gray-900">{activeSessions.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <span className="text-2xl">🚩</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Flags</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {activeSessions.reduce((sum, session) => sum + session.flags, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <span className="text-2xl">✅</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed Today</p>
                  <p className="text-2xl font-bold text-gray-900">15</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <span className="text-2xl">⏱️</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Avg Session Time</p>
                  <p className="text-2xl font-bold text-gray-900">1.5h</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Active Sessions */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Active Proctoring Sessions</CardTitle>
            <CardDescription>
              Currently monitored exam sessions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {activeSessions.length > 0 ? (
              <div className="space-y-4">
                {activeSessions.map((session) => (
                  <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg bg-white">
                    <div className="flex items-center space-x-4">
                      <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{session.studentName}</h3>
                        <p className="text-sm text-gray-600">{session.examTitle}</p>
                        <p className="text-sm text-gray-500">
                          Started: {session.startTime} • Duration: {session.duration}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      {session.flags > 0 && (
                        <div className="flex items-center space-x-1 px-2 py-1 bg-red-100 rounded-full">
                          <span className="text-red-600 text-sm">🚩</span>
                          <span className="text-red-600 text-sm font-medium">{session.flags}</span>
                        </div>
                      )}
                      <Link href={`/proctor/session/${session.id}`}>
                        <Button size="sm">Monitor</Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <span className="text-6xl mb-4 block">👁️</span>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Active Sessions</h3>
                <p className="text-gray-600">No exam sessions are currently being proctored.</p>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Flags */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Flags</CardTitle>
              <CardDescription>
                Latest flagged activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold text-gray-900">Face Not Visible</h3>
                    <p className="text-sm text-gray-600">Jane Smith - Physics Midterm</p>
                    <p className="text-sm text-gray-500">2 minutes ago</p>
                  </div>
                  <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                    High
                  </span>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold text-gray-900">Multiple Faces Detected</h3>
                    <p className="text-sm text-gray-600">Mike Johnson - Chemistry Quiz</p>
                    <p className="text-sm text-gray-500">15 minutes ago</p>
                  </div>
                  <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                    Medium
                  </span>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold text-gray-900">Looking Away</h3>
                    <p className="text-sm text-gray-600">Sarah Wilson - History Essay</p>
                    <p className="text-sm text-gray-500">1 hour ago</p>
                  </div>
                  <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                    Low
                  </span>
                </div>
              </div>
              <div className="mt-4">
                <Link href="/proctor/flags">
                  <Button variant="outline" className="w-full">
                    View All Flags
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common proctoring tasks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4">
                <Link href="/proctor/sessions">
                  <Button variant="outline" className="w-full h-16 flex items-center justify-start px-6">
                    <span className="text-2xl mr-4">📹</span>
                    <div className="text-left">
                      <div className="font-semibold">All Sessions</div>
                      <div className="text-sm opacity-80">View all proctoring sessions</div>
                    </div>
                  </Button>
                </Link>
                <Link href="/proctor/flags">
                  <Button variant="outline" className="w-full h-16 flex items-center justify-start px-6">
                    <span className="text-2xl mr-4">🚩</span>
                    <div className="text-left">
                      <div className="font-semibold">Flag Review</div>
                      <div className="text-sm opacity-80">Review flagged activities</div>
                    </div>
                  </Button>
                </Link>
                <Link href="/proctor/reports">
                  <Button variant="outline" className="w-full h-16 flex items-center justify-start px-6">
                    <span className="text-2xl mr-4">📊</span>
                    <div className="text-left">
                      <div className="font-semibold">Reports</div>
                      <div className="text-sm opacity-80">Generate proctoring reports</div>
                    </div>
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
