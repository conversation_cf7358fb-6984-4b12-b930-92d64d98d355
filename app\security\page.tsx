import { <PERSON>, CardContent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default function SecurityPage() {
  const securityFeatures = [
    {
      icon: "🔐",
      title: "End-to-End Encryption",
      description: "All data is encrypted in transit and at rest using AES-256 encryption",
      details: [
        "TLS 1.3 for data in transit",
        "AES-256 encryption for stored data",
        "Encrypted database connections",
        "Secure key management system"
      ]
    },
    {
      icon: "🛡️",
      title: "Advanced Authentication",
      description: "Multi-factor authentication and secure access controls",
      details: [
        "Multi-factor authentication (MFA)",
        "Single Sign-On (SSO) integration",
        "Role-based access control (RBAC)",
        "Session management and timeout"
      ]
    },
    {
      icon: "👁️",
      title: "AI-Powered Monitoring",
      description: "Real-time threat detection and behavioral analysis",
      details: [
        "Anomaly detection algorithms",
        "Behavioral pattern analysis",
        "Real-time threat monitoring",
        "Automated incident response"
      ]
    },
    {
      icon: "🏢",
      title: "Infrastructure Security",
      description: "Enterprise-grade hosting with multiple security layers",
      details: [
        "SOC 2 Type II certified data centers",
        "24/7 security monitoring",
        "DDoS protection and mitigation",
        "Regular penetration testing"
      ]
    },
    {
      icon: "📋",
      title: "Compliance & Auditing",
      description: "Meeting international standards and regulations",
      details: [
        "GDPR compliance for EU data protection",
        "FERPA compliance for educational records",
        "SOC 2 Type II certification",
        "Regular third-party security audits"
      ]
    },
    {
      icon: "🔄",
      title: "Backup & Recovery",
      description: "Comprehensive data protection and disaster recovery",
      details: [
        "Automated daily backups",
        "Geographic backup distribution",
        "Point-in-time recovery options",
        "99.9% uptime guarantee"
      ]
    }
  ]

  const certifications = [
    {
      name: "SOC 2 Type II",
      description: "Security, availability, and confidentiality controls",
      icon: "🏆"
    },
    {
      name: "GDPR Compliant",
      description: "European data protection regulation compliance",
      icon: "🇪🇺"
    },
    {
      name: "FERPA Compliant",
      description: "Educational records privacy protection",
      icon: "🎓"
    },
    {
      name: "ISO 27001",
      description: "Information security management system",
      icon: "📜"
    }
  ]

  const securityPractices = [
    {
      category: "Data Protection",
      practices: [
        "Data minimization - we only collect necessary information",
        "Purpose limitation - data used only for stated purposes",
        "Retention limits - automatic data deletion after retention period",
        "Data anonymization for analytics and research"
      ]
    },
    {
      category: "Access Control",
      practices: [
        "Principle of least privilege access",
        "Regular access reviews and audits",
        "Mandatory security training for all staff",
        "Background checks for personnel with data access"
      ]
    },
    {
      category: "Incident Response",
      practices: [
        "24/7 security operations center (SOC)",
        "Automated threat detection and response",
        "Incident response team with defined procedures",
        "Customer notification within 72 hours of any breach"
      ]
    },
    {
      category: "Continuous Improvement",
      practices: [
        "Regular security assessments and penetration testing",
        "Vulnerability management program",
        "Security awareness training for all employees",
        "Third-party security audits and certifications"
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Enterprise-Grade
            <span className="text-primary block">Security</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Your data and examinations are protected by multiple layers of security, 
            compliance certifications, and continuous monitoring to ensure the highest 
            level of protection and privacy.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" className="text-lg px-8 py-3">
                Security Consultation
              </Button>
            </Link>
            <Link href="/demo">
              <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                See Security Demo
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Security Features */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Comprehensive Security Framework
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Multi-layered security approach protecting every aspect of your online examinations
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {securityFeatures.map((feature, index) => (
              <Card key={index} className="h-full hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.details.map((detail, idx) => (
                      <li key={idx} className="flex items-start">
                        <span className="text-green-500 mr-2 mt-1">✓</span>
                        <span className="text-sm text-gray-600">{detail}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Certifications */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Certifications & Compliance
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Independently verified security standards and regulatory compliance
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {certifications.map((cert, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-8">
                  <div className="text-5xl mb-4">{cert.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{cert.name}</h3>
                  <p className="text-gray-600 text-sm">{cert.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Security Practices */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Security Best Practices
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our comprehensive approach to maintaining the highest security standards
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {securityPractices.map((category, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-xl">{category.category}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {category.practices.map((practice, idx) => (
                      <li key={idx} className="flex items-start">
                        <span className="text-primary mr-3 mt-1">•</span>
                        <span className="text-gray-600">{practice}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Security Architecture */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Security Architecture
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Layered defense strategy protecting your data at every level
            </p>
          </div>

          <div className="bg-gray-100 rounded-lg p-8">
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Multi-Layer Security Model</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white p-6 rounded-lg text-center">
                  <div className="text-3xl mb-3">🌐</div>
                  <h4 className="font-semibold text-gray-900 mb-2">Network Layer</h4>
                  <p className="text-gray-600 text-sm">Firewalls, DDoS protection, VPN access</p>
                </div>
                <div className="bg-white p-6 rounded-lg text-center">
                  <div className="text-3xl mb-3">🖥️</div>
                  <h4 className="font-semibold text-gray-900 mb-2">Application Layer</h4>
                  <p className="text-gray-600 text-sm">Secure coding, input validation, authentication</p>
                </div>
                <div className="bg-white p-6 rounded-lg text-center">
                  <div className="text-3xl mb-3">💾</div>
                  <h4 className="font-semibold text-gray-900 mb-2">Data Layer</h4>
                  <p className="text-gray-600 text-sm">Encryption, access controls, audit trails</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Incident Response */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                24/7 Security Monitoring
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                Our dedicated security operations center monitors your platform around the clock, 
                with automated threat detection and rapid incident response capabilities.
              </p>
              <ul className="space-y-3 mb-8">
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 mt-1">✓</span>
                  <span className="text-gray-600">Real-time threat detection and analysis</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 mt-1">✓</span>
                  <span className="text-gray-600">Automated incident response procedures</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 mt-1">✓</span>
                  <span className="text-gray-600">Immediate customer notification protocols</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 mt-1">✓</span>
                  <span className="text-gray-600">Forensic analysis and remediation</span>
                </li>
              </ul>
              <Link href="/contact">
                <Button size="lg">
                  Discuss Security Requirements
                </Button>
              </Link>
            </div>
            <div className="bg-gray-100 rounded-lg p-8">
              <div className="text-center">
                <div className="text-6xl mb-4">🛡️</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Security Operations Center</h3>
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-primary">24/7</div>
                    <div className="text-sm text-gray-600">Monitoring</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary">&lt;15min</div>
                    <div className="text-sm text-gray-600">Response Time</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary">99.9%</div>
                    <div className="text-sm text-gray-600">Threat Detection</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-primary">0</div>
                    <div className="text-sm text-gray-600">Data Breaches</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 bg-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Your Security is Our Priority
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Trust your online examinations to a platform built with security at its core. 
            Contact our security team to discuss your specific requirements.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
                Security Consultation
              </Button>
            </Link>
            <Link href="/signup">
              <Button size="lg" variant="outline" className="text-lg px-8 py-3 border-white text-white hover:bg-white hover:text-primary">
                Start Secure Trial
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
