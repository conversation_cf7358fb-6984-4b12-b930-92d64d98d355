"use client"

import { useAuth } from "@/contexts/auth-context"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function StudentDashboard() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push("/login")
    } else if (!loading && user && user.role !== "STUDENT") {
      router.push(`/${user.role.toLowerCase()}/dashboard`)
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const upcomingExams = [
    {
      id: "1",
      title: "Mathematics Final Exam",
      course: "Advanced Calculus",
      date: "2024-01-15",
      time: "10:00 AM",
      duration: "2 hours",
      status: "upcoming"
    },
    {
      id: "2", 
      title: "Physics Midterm",
      course: "Quantum Physics",
      date: "2024-01-18",
      time: "2:00 PM", 
      duration: "1.5 hours",
      status: "upcoming"
    }
  ]

  const recentResults = [
    {
      id: "1",
      title: "Chemistry Quiz",
      course: "Organic Chemistry",
      score: 85,
      maxScore: 100,
      date: "2024-01-10",
      status: "passed"
    },
    {
      id: "2",
      title: "History Essay",
      course: "World History",
      score: 92,
      maxScore: 100,
      date: "2024-01-08",
      status: "passed"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user.name || user.email}!
          </h1>
          <p className="text-gray-600 mt-2">
            Here's what's happening with your exams today.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <span className="text-2xl">📚</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Upcoming Exams</p>
                  <p className="text-2xl font-bold text-gray-900">{upcomingExams.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <span className="text-2xl">✅</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-gray-900">12</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <span className="text-2xl">⭐</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Average Score</p>
                  <p className="text-2xl font-bold text-gray-900">88%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <span className="text-2xl">🏆</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Rank</p>
                  <p className="text-2xl font-bold text-gray-900">#5</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Upcoming Exams */}
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Exams</CardTitle>
              <CardDescription>
                Your scheduled examinations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingExams.map((exam) => (
                  <div key={exam.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-semibold text-gray-900">{exam.title}</h3>
                      <p className="text-sm text-gray-600">{exam.course}</p>
                      <p className="text-sm text-gray-500">
                        {exam.date} at {exam.time} • {exam.duration}
                      </p>
                    </div>
                    <div className="flex flex-col gap-2">
                      <Link href={`/student/exam/${exam.id}/info`}>
                        <Button size="sm">View Details</Button>
                      </Link>
                    </div>
                  </div>
                ))}
                {upcomingExams.length === 0 && (
                  <p className="text-gray-500 text-center py-8">
                    No upcoming exams scheduled
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recent Results */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Results</CardTitle>
              <CardDescription>
                Your latest exam scores
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentResults.map((result) => (
                  <div key={result.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-semibold text-gray-900">{result.title}</h3>
                      <p className="text-sm text-gray-600">{result.course}</p>
                      <p className="text-sm text-gray-500">{result.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900">
                        {result.score}/{result.maxScore}
                      </p>
                      <p className="text-sm text-green-600">
                        {Math.round((result.score / result.maxScore) * 100)}%
                      </p>
                    </div>
                  </div>
                ))}
                {recentResults.length === 0 && (
                  <p className="text-gray-500 text-center py-8">
                    No recent results available
                  </p>
                )}
              </div>
              <div className="mt-4">
                <Link href="/student/results">
                  <Button variant="outline" className="w-full">
                    View All Results
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link href="/student/exams">
                <Button variant="outline" className="w-full h-20 flex flex-col">
                  <span className="text-2xl mb-2">📝</span>
                  <span>Browse Exams</span>
                </Button>
              </Link>
              <Link href="/student/results">
                <Button variant="outline" className="w-full h-20 flex flex-col">
                  <span className="text-2xl mb-2">📊</span>
                  <span>View Results</span>
                </Button>
              </Link>
              <Link href="/profile">
                <Button variant="outline" className="w-full h-20 flex flex-col">
                  <span className="text-2xl mb-2">👤</span>
                  <span>Update Profile</span>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
