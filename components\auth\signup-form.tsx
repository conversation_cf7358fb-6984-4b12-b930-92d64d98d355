"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuth } from "@/contexts/auth-context"
import { UserRole } from "@/types"

interface SignupFormProps {
  role: UserRole
  title: string
  description: string
  requiresAccessCode?: boolean
  accessCodeLabel?: string
}

export function SignupForm({ 
  role, 
  title, 
  description, 
  requiresAccessCode = false,
  accessCodeLabel = "Access code"
}: SignupFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const router = useRouter()
  const { signup } = useAuth()

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")
    setSuccess("")

    const formData = new FormData(e.currentTarget)
    const data = {
      name: formData.get("name") as string,
      email: formData.get("email") as string,
      password: formData.get("password") as string,
      confirmPassword: formData.get("confirmPassword") as string,
      role: role as UserRole,
      accessCode: requiresAccessCode ? formData.get("accessCode") as string : undefined
    }

    // Client-side validation
    if (data.password !== data.confirmPassword) {
      setError("Passwords do not match")
      setIsLoading(false)
      return
    }

    const result = await signup({
      email: data.email,
      password: data.password,
      name: data.name,
      role: data.role
    })

    if (result.success) {
      setSuccess("Account created successfully! Redirecting to login...")
      setTimeout(() => {
        router.push("/login?message=Account created successfully. Please sign in.")
      }, 2000)
    } else {
      setError(result.error || "Signup failed")
    }

    setIsLoading(false)
  }

  const getRoleIcon = () => {
    switch (role) {
      case "ADMIN": return "🔐"
      case "INSTRUCTOR": return "👨‍🏫"
      case "PROCTOR": return "👁️"
      default: return "🎓"
    }
  }

  const getRoleColor = () => {
    switch (role) {
      case "ADMIN": return "bg-red-100"
      case "INSTRUCTOR": return "bg-blue-100"
      case "PROCTOR": return "bg-green-100"
      default: return "bg-purple-100"
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className={`w-16 h-16 ${getRoleColor()} rounded-full flex items-center justify-center mx-auto mb-4`}>
            <span className="text-2xl">{getRoleIcon()}</span>
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            {title}
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {description}
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Create Account</CardTitle>
            <CardDescription>
              Fill in your information to get started
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {success && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-600">{success}</p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Full name
                </label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  autoComplete="name"
                  required
                  className="mt-1"
                  placeholder="Enter your full name"
                  disabled={isLoading}
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email address
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="mt-1"
                  placeholder="Enter your email"
                  disabled={isLoading}
                />
              </div>

              {requiresAccessCode && (
                <div>
                  <label htmlFor="accessCode" className="block text-sm font-medium text-gray-700">
                    {accessCodeLabel}
                  </label>
                  <Input
                    id="accessCode"
                    name="accessCode"
                    type="password"
                    required
                    className="mt-1"
                    placeholder={`Enter ${accessCodeLabel.toLowerCase()}`}
                    disabled={isLoading}
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Contact your administrator for the access code
                  </p>
                </div>
              )}

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  className="mt-1"
                  placeholder="Create a strong password"
                  disabled={isLoading}
                />
                <p className="mt-1 text-xs text-gray-500">
                  Must be at least 8 characters with uppercase, lowercase, number, and special character
                </p>
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                  Confirm password
                </label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  autoComplete="new-password"
                  required
                  className="mt-1"
                  placeholder="Confirm your password"
                  disabled={isLoading}
                />
              </div>

              <div className="flex items-center">
                <input
                  id="terms"
                  name="terms"
                  type="checkbox"
                  required
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  disabled={isLoading}
                />
                <label htmlFor="terms" className="ml-2 block text-sm text-gray-900">
                  I agree to the{" "}
                  <Link href="/terms" className="text-primary hover:text-primary/80">
                    Terms of Service
                  </Link>{" "}
                  and{" "}
                  <Link href="/privacy" className="text-primary hover:text-primary/80">
                    Privacy Policy
                  </Link>
                </label>
              </div>

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Creating Account..." : "Create Account"}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Already have an account?{" "}
                <Link href="/login" className="font-medium text-primary hover:text-primary/80">
                  Sign in here
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
