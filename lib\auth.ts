import { NextAuthOptions, User as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { db } from "./db"
import bcrypt from "bcryptjs"
import { UserRole } from "./middleware"

// Extend NextAuth User type to include 'role'
declare module "next-auth" {
  interface User {
    role: UserRole
    id: string
  }
  interface Session {
    user: {
      id: string
      role: UserRole
      name?: string | null
      email?: string | null
      image?: string | null
    }
  }
  interface JWT {
    id?: string
    role?: UserRole
  }
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(db) as any,
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await db.user.findUnique({
          where: {
            email: credentials.email
          },
          include: {
            profile: true
          }
        })

        if (!user || !user.password) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        if (!user.isActive) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          image: user.image,
          emailVerified: user.emailVerified
        }
      }
    })
  ],
  session: {
    strategy: "jwt"
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
        token.id = user.id
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.role = token.role as UserRole
      }
      return session
    }
  },
  pages: {
    signIn: "/login",
    error: "/auth/error"
  },
  secret: process.env.NEXTAUTH_SECRET
}

// Helper function to get user role
export async function getUserRole(userId: string): Promise<UserRole | null> {
  try {
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { role: true }
    })
    return user?.role || null
  } catch (error) {
    console.error("Error fetching user role:", error)
    return null
  }
}

// Helper function to check if user has required role
export function hasRole(userRole: UserRole, requiredRoles: UserRole[]): boolean {
  return requiredRoles.includes(userRole)
}

// Role hierarchy for permission checking
export const roleHierarchy: Record<UserRole, number> = {
  STUDENT: 1,
  INSTRUCTOR: 2,
  PROCTOR: 3,
  ADMIN: 4
}

export function hasMinimumRole(userRole: UserRole, minimumRole: UserRole): boolean {
  return roleHierarchy[userRole] >= roleHierarchy[minimumRole]
}
