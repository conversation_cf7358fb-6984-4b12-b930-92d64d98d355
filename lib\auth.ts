// import { NextAuthOptions } from "next-auth"
// import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials"
// import { PrismaAdapter } from "@auth/prisma-adapter"
// import { db } from "./db"
// import bcrypt from "bcryptjs"
import { UserRole } from "./middleware"

// TODO: Uncomment and configure when NextAuth and Prisma are properly installed
/*
export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(db) as any,
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        // Implementation will be added when dependencies are installed
        return null
      }
    })
  ],
  session: {
    strategy: "jwt"
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
        token.id = user.id
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.role = token.role as UserRole
      }
      return session
    }
  },
  pages: {
    signIn: "/login",
    signUp: "/signup",
    error: "/auth/error"
  },
  secret: process.env.NEXTAUTH_SECRET
}
*/

// Helper function to get user role - simplified for now
export async function getUserRole(userId: string): Promise<UserRole | null> {
  try {
    // TODO: Implement when database is connected
    return null
  } catch (error) {
    console.error("Error fetching user role:", error)
    return null
  }
}

// Helper function to check if user has required role
export function hasRole(userRole: UserRole, requiredRoles: UserRole[]): boolean {
  return requiredRoles.includes(userRole)
}

// Role hierarchy for permission checking
export const roleHierarchy: Record<UserRole, number> = {
  STUDENT: 1,
  INSTRUCTOR: 2,
  PROCTOR: 3,
  ADMIN: 4
}

export function hasMinimumRole(userRole: UserRole, minimumRole: UserRole): boolean {
  return roleHierarchy[userRole] >= roleHierarchy[minimumRole]
}
