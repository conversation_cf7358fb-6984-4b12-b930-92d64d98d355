// import { PrismaClient } from '@prisma/client'

// Temporary mock database until Prisma is properly installed
export const db = {
  user: {
    findUnique: async () => null,
    create: async () => null,
    update: async () => null,
    delete: async () => null,
  },
  auditLog: {
    create: async () => null,
  },
  verificationToken: {
    findUnique: async () => null,
    create: async () => null,
    delete: async () => null,
  }
}

// TODO: Replace with actual Prisma client when dependencies are installed
/*
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const db =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: ['query'],
  })

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db
*/
