import nodemailer from 'nodemailer'

// Create transporter
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
})

// Email templates
const getEmailTemplate = (type: string, data: any) => {
  const baseUrl = process.env.APP_URL || 'http://localhost:3000'
  
  switch (type) {
    case 'password-reset':
      return {
        subject: 'Reset Your Password - Online Exam Platform',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
              <h1 style="color: #333; margin: 0;">Online Exam Platform</h1>
            </div>
            
            <div style="padding: 30px 20px;">
              <h2 style="color: #333;">Password Reset Request</h2>
              
              <p>Hello ${data.name},</p>
              
              <p>We received a request to reset your password for your Online Exam Platform account.</p>
              
              <p>Click the button below to reset your password:</p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${baseUrl}/reset-password?token=${data.token}" 
                   style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                  Reset Password
                </a>
              </div>
              
              <p>Or copy and paste this link into your browser:</p>
              <p style="word-break: break-all; color: #007bff;">
                ${baseUrl}/reset-password?token=${data.token}
              </p>
              
              <p><strong>This link will expire in 1 hour.</strong></p>
              
              <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>
              
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              
              <p style="color: #666; font-size: 14px;">
                This email was sent from Online Exam Platform. If you have any questions, please contact our support team.
              </p>
            </div>
          </div>
        `
      }
      
    case 'email-verification':
      return {
        subject: 'Verify Your Email - Online Exam Platform',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
              <h1 style="color: #333; margin: 0;">Online Exam Platform</h1>
            </div>
            
            <div style="padding: 30px 20px;">
              <h2 style="color: #333;">Verify Your Email Address</h2>
              
              <p>Hello ${data.name},</p>
              
              <p>Thank you for signing up for Online Exam Platform! Please verify your email address to complete your registration.</p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${baseUrl}/verify-email?token=${data.token}" 
                   style="background-color: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                  Verify Email
                </a>
              </div>
              
              <p>Or copy and paste this link into your browser:</p>
              <p style="word-break: break-all; color: #28a745;">
                ${baseUrl}/verify-email?token=${data.token}
              </p>
              
              <p><strong>This link will expire in 24 hours.</strong></p>
              
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              
              <p style="color: #666; font-size: 14px;">
                This email was sent from Online Exam Platform. If you have any questions, please contact our support team.
              </p>
            </div>
          </div>
        `
      }
      
    case 'welcome':
      return {
        subject: 'Welcome to Online Exam Platform!',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
              <h1 style="color: #333; margin: 0;">Online Exam Platform</h1>
            </div>
            
            <div style="padding: 30px 20px;">
              <h2 style="color: #333;">Welcome to Online Exam Platform!</h2>
              
              <p>Hello ${data.name},</p>
              
              <p>Welcome to Online Exam Platform! Your account has been successfully created.</p>
              
              <p><strong>Account Details:</strong></p>
              <ul>
                <li>Email: ${data.email}</li>
                <li>Role: ${data.role}</li>
                <li>Registration Date: ${new Date().toLocaleDateString()}</li>
              </ul>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${baseUrl}/${data.role.toLowerCase()}/dashboard" 
                   style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                  Go to Dashboard
                </a>
              </div>
              
              <p>Next steps:</p>
              <ol>
                <li>Complete your profile setup</li>
                <li>Explore the platform features</li>
                <li>Contact support if you need any assistance</li>
              </ol>
              
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              
              <p style="color: #666; font-size: 14px;">
                This email was sent from Online Exam Platform. If you have any questions, please contact our support team.
              </p>
            </div>
          </div>
        `
      }
      
    default:
      throw new Error(`Unknown email template: ${type}`)
  }
}

// Send email function
export async function sendEmail(to: string, type: string, data: any) {
  try {
    const template = getEmailTemplate(type, data)
    
    const mailOptions = {
      from: `"${process.env.APP_NAME}" <${process.env.SMTP_USER}>`,
      to,
      subject: template.subject,
      html: template.html,
    }

    const result = await transporter.sendMail(mailOptions)
    console.log('Email sent successfully:', result.messageId)
    return result
  } catch (error) {
    console.error('Error sending email:', error)
    throw error
  }
}

// Specific email functions
export async function sendPasswordResetEmail(email: string, name: string, token: string) {
  return sendEmail(email, 'password-reset', { name, token })
}

export async function sendEmailVerification(email: string, name: string, token: string) {
  return sendEmail(email, 'email-verification', { name, token })
}

export async function sendWelcomeEmail(email: string, name: string, role: string) {
  return sendEmail(email, 'welcome', { name, email, role })
}
