import { NextRequest, NextResponse } from "next/server"
import jwt from "jsonwebtoken"
import { UserRole } from "@prisma/client"

export interface AuthUser {
  userId: string
  email: string
  role: UserRole
}

// Verify JWT token
export function verifyToken(token: string): AuthUser | null {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
    return {
      userId: decoded.userId,
      email: decoded.email,
      role: decoded.role
    }
  } catch (error) {
    return null
  }
}

// Get user from request
export function getUserFromRequest(request: NextRequest): AuthUser | null {
  const token = request.cookies.get("auth-token")?.value
  if (!token) return null
  
  return verifyToken(token)
}

// Authentication middleware
export function withAuth(handler: (request: NextRequest, user: AuthUser) => Promise<NextResponse>) {
  return async (request: NextRequest) => {
    const user = getUserFromRequest(request)
    
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }
    
    return handler(request, user)
  }
}

// Role-based authorization middleware
export function withRole(roles: UserRole[], handler: (request: NextRequest, user: AuthUser) => Promise<NextResponse>) {
  return withAuth(async (request: NextRequest, user: AuthUser) => {
    if (!roles.includes(user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }
    
    return handler(request, user)
  })
}

// Admin only middleware
export function withAdmin(handler: (request: NextRequest, user: AuthUser) => Promise<NextResponse>) {
  return withRole([UserRole.ADMIN], handler)
}

// Instructor or Admin middleware
export function withInstructorOrAdmin(handler: (request: NextRequest, user: AuthUser) => Promise<NextResponse>) {
  return withRole([UserRole.INSTRUCTOR, UserRole.ADMIN], handler)
}

// Student only middleware
export function withStudent(handler: (request: NextRequest, user: AuthUser) => Promise<NextResponse>) {
  return withRole([UserRole.STUDENT], handler)
}

// Proctor or Admin middleware
export function withProctorOrAdmin(handler: (request: NextRequest, user: AuthUser) => Promise<NextResponse>) {
  return withRole([UserRole.PROCTOR, UserRole.ADMIN], handler)
}
