{"name": "online-exam-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.10.1", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "next": "15.3.4", "nodemailer": "^7.0.3", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}