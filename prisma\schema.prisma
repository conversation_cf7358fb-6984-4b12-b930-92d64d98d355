// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  emailVerified DateTime?
  name          String?
  image         String?
  password      String?
  role          UserRole  @default(STUDENT)
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Profile information
  profile       UserProfile?
  
  // Authentication
  accounts      Account[]
  sessions      Session[]
  
  // Role-specific relations
  studentExams     StudentExam[]
  instructorCourses Course[] @relation("InstructorCourses")
  proctorSessions  ProctorSession[]
  
  // Activity logs
  auditLogs     AuditLog[]
  notifications Notification[]

  // Created content
  createdQuestions Question[] @relation("CreatedQuestions")

  @@map("users")
}

model UserProfile {
  id              String   @id @default(cuid())
  userId          String   @unique
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  firstName       String?
  lastName        String?
  phone           String?
  dateOfBirth     DateTime?
  address         String?
  
  // ID Verification
  idType          String?   // "passport", "license", "national_id"
  idNumber        String?
  idImageUrl      String?
  idVerified      Boolean   @default(false)
  idVerifiedAt    DateTime?
  
  // Device & Environment
  deviceFingerprint String?
  lastDeviceCheck   DateTime?
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("user_profiles")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Course and Exam Management
model Course {
  id           String   @id @default(cuid())
  title        String
  description  String?
  code         String   @unique
  instructorId String
  instructor   User     @relation("InstructorCourses", fields: [instructorId], references: [id])
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  exams        Exam[]

  @@map("courses")
}

model Exam {
  id              String     @id @default(cuid())
  title           String
  description     String?
  courseId        String
  course          Course     @relation(fields: [courseId], references: [id])
  
  // Exam Configuration
  duration        Int        // in minutes
  totalMarks      Int
  passingMarks    Int
  maxAttempts     Int        @default(1)
  
  // Scheduling
  startTime       DateTime
  endTime         DateTime
  
  // Settings
  shuffleQuestions Boolean   @default(false)
  showResults     Boolean   @default(true)
  allowReview     Boolean   @default(false)
  requireProctor  Boolean   @default(false)
  
  // Status
  status          ExamStatus @default(DRAFT)
  isActive        Boolean    @default(true)
  
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // Relations
  questions       ExamQuestion[]
  studentExams    StudentExam[]
  proctorSessions ProctorSession[]

  @@map("exams")
}

// Enums
enum UserRole {
  STUDENT
  INSTRUCTOR
  PROCTOR
  ADMIN
}

enum ExamStatus {
  DRAFT
  PUBLISHED
  ACTIVE
  COMPLETED
  CANCELLED
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  SHORT_ANSWER
  ESSAY
  FILL_BLANK
}

enum StudentExamStatus {
  NOT_STARTED
  IN_PROGRESS
  SUBMITTED
  GRADED
  CANCELLED
}

enum ProctorFlag {
  FACE_NOT_VISIBLE
  MULTIPLE_FACES
  LOOKING_AWAY
  SUSPICIOUS_ACTIVITY
  TECHNICAL_ISSUE
  MANUAL_FLAG
}

// Question Bank
model Question {
  id          String       @id @default(cuid())
  title       String
  content     String
  type        QuestionType
  difficulty  Int          @default(1) // 1-5 scale
  points      Int          @default(1)

  // Question options (for MCQ, True/False)
  options     QuestionOption[]

  // Metadata
  tags        String // JSON string for SQLite compatibility
  subject     String?
  topic       String?

  createdBy   String
  creator     User         @relation("CreatedQuestions", fields: [createdBy], references: [id])

  isActive    Boolean      @default(true)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relations
  examQuestions ExamQuestion[]

  @@map("questions")
}

model QuestionOption {
  id         String   @id @default(cuid())
  questionId String
  question   Question @relation(fields: [questionId], references: [id], onDelete: Cascade)

  text       String
  isCorrect  Boolean  @default(false)
  order      Int      @default(0)

  createdAt  DateTime @default(now())

  @@map("question_options")
}

model ExamQuestion {
  id         String   @id @default(cuid())
  examId     String
  exam       Exam     @relation(fields: [examId], references: [id], onDelete: Cascade)
  questionId String
  question   Question @relation(fields: [questionId], references: [id])

  order      Int
  points     Int      @default(1)

  createdAt  DateTime @default(now())

  // Student answers
  studentAnswers StudentAnswer[]

  @@unique([examId, questionId])
  @@map("exam_questions")
}

// Student Exam Management
model StudentExam {
  id              String            @id @default(cuid())
  studentId       String
  student         User              @relation(fields: [studentId], references: [id])
  examId          String
  exam            Exam              @relation(fields: [examId], references: [id])

  // Attempt tracking
  attemptNumber   Int               @default(1)
  status          StudentExamStatus @default(NOT_STARTED)

  // Timing
  startedAt       DateTime?
  submittedAt     DateTime?
  timeSpent       Int?              // in seconds

  // Scoring
  totalScore      Int?
  maxScore        Int?
  percentage      Float?
  passed          Boolean?

  // Proctoring
  requiresProctor Boolean           @default(false)
  proctorApproved Boolean?

  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  answers         StudentAnswer[]
  proctorSession  ProctorSession?

  @@unique([studentId, examId, attemptNumber])
  @@map("student_exams")
}

model StudentAnswer {
  id               String       @id @default(cuid())
  studentExamId    String
  studentExam      StudentExam  @relation(fields: [studentExamId], references: [id], onDelete: Cascade)
  examQuestionId   String
  examQuestion     ExamQuestion @relation(fields: [examQuestionId], references: [id])

  // Answer content
  selectedOptions  String       // JSON string for SQLite compatibility
  textAnswer       String?      // For text-based questions

  // Metadata
  timeSpent        Int?         // seconds spent on this question
  flaggedForReview Boolean      @default(false)

  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt

  @@unique([studentExamId, examQuestionId])
  @@map("student_answers")
}

// Proctoring System
model ProctorSession {
  id            String   @id @default(cuid())
  studentExamId String   @unique
  studentExam   StudentExam @relation(fields: [studentExamId], references: [id])
  proctorId     String?
  proctor       User?    @relation(fields: [proctorId], references: [id])
  examId        String
  exam          Exam     @relation(fields: [examId], references: [id])

  // Session data
  startedAt     DateTime
  endedAt       DateTime?

  // Recording
  recordingUrl  String?
  screenshotUrls String // JSON string for SQLite compatibility

  // Status
  isActive      Boolean  @default(true)

  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  events        ProctorEvent[]

  @@map("proctor_sessions")
}

model ProctorEvent {
  id              String         @id @default(cuid())
  sessionId       String
  session         ProctorSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  type            ProctorFlag
  description     String?
  severity        Int            @default(1) // 1-5 scale

  // Evidence
  screenshotUrl   String?
  videoTimestamp  Int?           // seconds from start

  // Resolution
  resolved        Boolean        @default(false)
  resolvedBy      String?
  resolvedAt      DateTime?
  resolution      String?

  createdAt       DateTime       @default(now())

  @@map("proctor_events")
}

// System Management
model AuditLog {
  id          String   @id @default(cuid())
  userId      String?
  user        User?    @relation(fields: [userId], references: [id])

  action      String   // "CREATE", "UPDATE", "DELETE", "LOGIN", etc.
  resource    String   // "USER", "EXAM", "QUESTION", etc.
  resourceId  String?

  details     Json?    // Additional context
  ipAddress   String?
  userAgent   String?

  createdAt   DateTime @default(now())

  @@map("audit_logs")
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  title     String
  message   String
  type      String   // "INFO", "WARNING", "ERROR", "SUCCESS"

  read      Boolean  @default(false)
  readAt    DateTime?

  // Optional action
  actionUrl String?
  actionText String?

  createdAt DateTime @default(now())

  @@map("notifications")
}
