// Import UserRole from middleware until Prisma client is properly generated
import { UserRole } from "@/lib/middleware"

// Define other enums locally
export type ExamStatus = "DRAFT" | "PUBLISHED" | "ACTIVE" | "COMPLETED" | "CANCELLED"
export type QuestionType = "MULTIPLE_CHOICE" | "TRUE_FALSE" | "SHORT_ANSWER" | "ESSAY" | "FILL_BLANK"
export type StudentExamStatus = "NOT_STARTED" | "IN_PROGRESS" | "SUBMITTED" | "GRADED" | "CANCELLED"
export type ProctorFlag = "FACE_NOT_VISIBLE" | "MULTIPLE_FACES" | "LOOKING_AWAY" | "SUSPICIOUS_ACTIVITY" | "TECHNICAL_ISSUE" | "MANUAL_FLAG"

// Re-export UserRole
export { UserRole }

// Extended User types
export interface UserWithProfile {
  id: string
  email: string
  name: string | null
  role: UserRole
  isActive: boolean
  profile?: UserProfile | null
  createdAt: Date
  updatedAt: Date
}

export interface UserProfile {
  id: string
  firstName: string | null
  lastName: string | null
  phone: string | null
  dateOfBirth: Date | null
  address: string | null
  idType: string | null
  idNumber: string | null
  idImageUrl: string | null
  idVerified: boolean
  idVerifiedAt: Date | null
  deviceFingerprint: string | null
  lastDeviceCheck: Date | null
}

// Exam related types
export interface ExamWithDetails {
  id: string
  title: string
  description: string | null
  duration: number
  totalMarks: number
  passingMarks: number
  maxAttempts: number
  startTime: Date
  endTime: Date
  shuffleQuestions: boolean
  showResults: boolean
  allowReview: boolean
  requireProctor: boolean
  status: ExamStatus
  isActive: boolean
  course: {
    id: string
    title: string
    code: string
    instructor: {
      id: string
      name: string | null
      email: string
    }
  }
  questions: ExamQuestionWithDetails[]
  _count?: {
    studentExams: number
  }
}

export interface ExamQuestionWithDetails {
  id: string
  order: number
  points: number
  question: {
    id: string
    title: string
    content: string
    type: QuestionType
    difficulty: number
    options: QuestionOption[]
  }
}

export interface QuestionOption {
  id: string
  text: string
  isCorrect: boolean
  order: number
}

export interface QuestionWithOptions {
  id: string
  title: string
  content: string
  type: QuestionType
  difficulty: number
  points: number
  tags: string[]
  subject: string | null
  topic: string | null
  options: QuestionOption[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Student Exam types
export interface StudentExamWithDetails {
  id: string
  attemptNumber: number
  status: StudentExamStatus
  startedAt: Date | null
  submittedAt: Date | null
  timeSpent: number | null
  totalScore: number | null
  maxScore: number | null
  percentage: number | null
  passed: boolean | null
  exam: {
    id: string
    title: string
    duration: number
    totalMarks: number
    passingMarks: number
    startTime: Date
    endTime: Date
    course: {
      title: string
      code: string
    }
  }
  answers: StudentAnswerWithQuestion[]
  proctorSession?: ProctorSessionWithDetails | null
}

export interface StudentAnswerWithQuestion {
  id: string
  selectedOptions: string[]
  textAnswer: string | null
  timeSpent: number | null
  flaggedForReview: boolean
  examQuestion: {
    id: string
    order: number
    points: number
    question: {
      id: string
      title: string
      content: string
      type: QuestionType
      options: QuestionOption[]
    }
  }
}

// Proctoring types
export interface ProctorSessionWithDetails {
  id: string
  startedAt: Date
  endedAt: Date | null
  recordingUrl: string | null
  screenshotUrls: string[]
  isActive: boolean
  proctor?: {
    id: string
    name: string | null
    email: string
  } | null
  studentExam: {
    id: string
    student: {
      id: string
      name: string | null
      email: string
    }
    exam: {
      id: string
      title: string
      duration: number
    }
  }
  events: ProctorEventWithDetails[]
}

export interface ProctorEventWithDetails {
  id: string
  type: ProctorFlag
  description: string | null
  severity: number
  screenshotUrl: string | null
  videoTimestamp: number | null
  resolved: boolean
  resolvedBy: string | null
  resolvedAt: Date | null
  resolution: string | null
  createdAt: Date
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Form types
export interface LoginForm {
  email: string
  password: string
}

export interface SignupForm {
  email: string
  password: string
  confirmPassword: string
  name: string
  role: UserRole
}

export interface ProfileSetupForm {
  firstName: string
  lastName: string
  phone?: string
  dateOfBirth?: Date
  address?: string
}

export interface ExamCreationForm {
  title: string
  description?: string
  courseId: string
  duration: number
  totalMarks: number
  passingMarks: number
  maxAttempts: number
  startTime: Date
  endTime: Date
  shuffleQuestions: boolean
  showResults: boolean
  allowReview: boolean
  requireProctor: boolean
}

export interface QuestionCreationForm {
  title: string
  content: string
  type: QuestionType
  difficulty: number
  points: number
  tags: string[]
  subject?: string
  topic?: string
  options: Omit<QuestionOption, 'id'>[]
}

// Device check types
export interface DeviceCheckResult {
  camera: {
    available: boolean
    permission: boolean
    error?: string
  }
  microphone: {
    available: boolean
    permission: boolean
    error?: string
  }
  screen: {
    resolution: string
    fullscreenCapable: boolean
  }
  network: {
    speed: number
    stable: boolean
  }
  browser: {
    name: string
    version: string
    compatible: boolean
  }
  overall: {
    passed: boolean
    issues: string[]
  }
}

// Notification types
export interface NotificationData {
  id: string
  title: string
  message: string
  type: 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS'
  read: boolean
  readAt: Date | null
  actionUrl: string | null
  actionText: string | null
  createdAt: Date
}
